package main

import (
	"context"
	"fmt"
	"log"

	"go-api-solve/internal/config"
	"go-api-solve/internal/repository"
	"go-api-solve/pkg/ai"
	"go-api-solve/pkg/database"
)

func main() {
	fmt.Println("=== Qwen测试程序 ===")
	
	// 加载配置
	cfg := config.LoadConfig()
	
	// 初始化数据库连接
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 自动迁移数据库表
	if err := database.AutoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化仓库层
	db := database.GetDB()
	modelConfigRepo := repository.NewModelConfigRepository(db)

	// 测试获取Qwen模型配置
	fmt.Println("\n1. 测试获取Qwen模型配置...")
	config, err := modelConfigRepo.GetByModelName("qwen-vl-plus")
	if err != nil {
		fmt.Printf("❌ 获取Qwen模型配置失败: %v\n", err)
		
		// 尝试获取所有配置
		fmt.Println("\n2. 获取所有模型配置...")
		configs, err := modelConfigRepo.GetAll()
		if err != nil {
			fmt.Printf("❌ 获取所有模型配置失败: %v\n", err)
		} else {
			fmt.Printf("✅ 找到 %d 个模型配置:\n", len(configs))
			for _, cfg := range configs {
				fmt.Printf("   - %s (活跃: %d)\n", cfg.ModelName, cfg.IsActive)
			}
		}
		return
	}
	
	fmt.Printf("✅ 成功获取Qwen模型配置:\n")
	fmt.Printf("   模型名称: %s\n", config.ModelName)
	fmt.Printf("   System角色: %s\n", config.RoleSystem[:min(50, len(config.RoleSystem))]+"...")
	fmt.Printf("   User角色: %s\n", config.RoleUser[:min(50, len(config.RoleUser))]+"...")
	fmt.Printf("   温度: %.2f\n", config.Temperature)
	fmt.Printf("   TopP: %.2f\n", config.TopP)
	fmt.Printf("   响应格式: %s\n", config.ResponseFormat)

	// 测试Qwen API调用
	fmt.Println("\n3. 测试Qwen API调用...")
	qwenClient := ai.NewQwenClient(cfg.AI.QwenKey)
	
	// 使用一个测试图片URL
	testImageURL := "http://img.igmdns.com/img/c_01.jpg"
	fmt.Printf("   测试图片: %s\n", testImageURL)
	
	ctx := context.Background()
	response, err := qwenClient.CallQwenVLPlus(ctx, testImageURL, config)
	if err != nil {
		fmt.Printf("❌ Qwen API调用失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Qwen API调用成功!\n")
	fmt.Printf("   Token使用: 输入=%d, 输出=%d, 总计=%d\n", 
		response.Usage.InputTokens, 
		response.Usage.OutputTokens, 
		response.Usage.TotalTokens)
	
	// 提取内容
	fmt.Println("\n4. 提取响应内容...")
	content, err := ai.ExtractContentFromQwenResponse(response)
	if err != nil {
		fmt.Printf("❌ 提取内容失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 成功提取内容:\n")
	fmt.Printf("   内容长度: %d 字符\n", len(content))
	fmt.Printf("   内容预览: %s\n", content[:min(200, len(content))]+"...")
	
	fmt.Println("\n=== 测试完成 ===")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
